import * as THREE from 'three'
import { AudioManager } from './AudioManager'

interface BottleData {
  mesh: THREE.Group
  message: string
  velocity: THREE.Vector3
  life: number
  maxLife: number
}

export class DriftBottle {
  private scene: THREE.Scene
  private camera: THREE.PerspectiveCamera
  private bottles: BottleData[] = []
  private currentBottle: THREE.Group | null = null
  private isThrowingBottle: boolean = false
  
  private emotionInput: HTMLTextAreaElement
  private createBottleBtn: HTMLButtonElement
  private throwBottleBtn: HTMLButtonElement

  constructor(scene: THREE.Scene, camera: THREE.PerspectiveCamera) {
    this.scene = scene
    this.camera = camera
    
    this.setupUI()
    this.setupEventListeners()
  }

  private setupUI(): void {
    this.emotionInput = document.getElementById('emotion-input') as HTMLTextAreaElement
    this.createBottleBtn = document.getElementById('create-bottle-btn') as HTMLButtonElement
    this.throwBottleBtn = document.getElementById('throw-bottle-btn') as HTMLButtonElement
  }

  private setupEventListeners(): void {
    this.createBottleBtn.addEventListener('click', () => {
      this.createBottle()
    })
    
    this.throwBottleBtn.addEventListener('click', () => {
      this.throwBottle()
    })
    
    // 监听输入变化
    this.emotionInput.addEventListener('input', () => {
      const hasText = this.emotionInput.value.trim().length > 0
      this.createBottleBtn.disabled = !hasText
    })
  }

  private createBottle(): void {
    const message = this.emotionInput.value.trim()
    if (!message) return
    
    // 如果已有瓶子，先移除
    if (this.currentBottle) {
      this.scene.remove(this.currentBottle)
    }
    
    // 创建新瓶子
    this.currentBottle = this.createBottleGeometry()
    this.currentBottle.position.set(2, 1, 5) // 在用户面前
    this.scene.add(this.currentBottle)
    
    // 更新UI状态
    this.createBottleBtn.disabled = true
    this.throwBottleBtn.disabled = false
    this.emotionInput.disabled = true
    
    // 添加创建动画
    this.animateBottleCreation()
  }

  private createBottleGeometry(): THREE.Group {
    const bottleGroup = new THREE.Group()
    
    // 瓶身
    const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.4, 1.2, 12)
    const bodyMaterial = new THREE.MeshPhysicalMaterial({
      color: 0x87ceeb,
      transparent: true,
      opacity: 0.7,
      roughness: 0.1,
      metalness: 0.1,
      transmission: 0.9,
      thickness: 0.1
    })
    
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial)
    body.position.y = 0.6
    body.castShadow = true
    bottleGroup.add(body)
    
    // 瓶颈
    const neckGeometry = new THREE.CylinderGeometry(0.15, 0.2, 0.4, 8)
    const neckMaterial = bodyMaterial.clone()
    
    const neck = new THREE.Mesh(neckGeometry, neckMaterial)
    neck.position.y = 1.4
    neck.castShadow = true
    bottleGroup.add(neck)
    
    // 瓶塞
    const corkGeometry = new THREE.CylinderGeometry(0.18, 0.18, 0.2, 8)
    const corkMaterial = new THREE.MeshLambertMaterial({
      color: 0x8B4513
    })
    
    const cork = new THREE.Mesh(corkGeometry, corkMaterial)
    cork.position.y = 1.7
    cork.castShadow = true
    bottleGroup.add(cork)
    
    // 纸条（在瓶子内部）
    const paperGeometry = new THREE.PlaneGeometry(0.4, 0.6)
    const paperMaterial = new THREE.MeshLambertMaterial({
      color: 0xFFFFF0,
      transparent: true,
      opacity: 0.8,
      side: THREE.DoubleSide
    })
    
    const paper = new THREE.Mesh(paperGeometry, paperMaterial)
    paper.position.set(0, 0.5, 0)
    paper.rotation.y = Math.PI / 4
    bottleGroup.add(paper)
    
    // 添加发光效果
    const glowGeometry = new THREE.SphereGeometry(0.6, 16, 16)
    const glowMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        color: { value: new THREE.Color(0x26d0ce) }
      },
      vertexShader: `
        varying vec3 vNormal;
        uniform float time;
        
        void main() {
          vNormal = normalize(normalMatrix * normal);
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec3 color;
        varying vec3 vNormal;
        
        void main() {
          float intensity = pow(0.6 - dot(vNormal, vec3(0.0, 0.0, 1.0)), 2.0);
          vec3 glow = color * intensity;
          
          float pulse = sin(time * 3.0) * 0.2 + 0.8;
          glow *= pulse;
          
          gl_FragColor = vec4(glow, intensity * 0.3);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending,
      side: THREE.BackSide
    })
    
    const glow = new THREE.Mesh(glowGeometry, glowMaterial)
    glow.position.y = 0.6
    bottleGroup.add(glow)
    
    return bottleGroup
  }

  private animateBottleCreation(): void {
    if (!this.currentBottle) return
    
    // 从小到大的缩放动画
    this.currentBottle.scale.setScalar(0.1)
    
    const animate = () => {
      if (!this.currentBottle) return
      
      const currentScale = this.currentBottle.scale.x
      const targetScale = 1
      const newScale = THREE.MathUtils.lerp(currentScale, targetScale, 0.1)
      
      this.currentBottle.scale.setScalar(newScale)
      
      if (Math.abs(newScale - targetScale) > 0.01) {
        requestAnimationFrame(animate)
      }
    }
    
    animate()
  }

  private throwBottle(): void {
    if (!this.currentBottle || this.isThrowingBottle) return
    
    this.isThrowingBottle = true
    const message = this.emotionInput.value.trim()
    
    // 创建投掷动画
    const startPosition = this.currentBottle.position.clone()
    const endPosition = new THREE.Vector3(
      (Math.random() - 0.5) * 20,
      -2,
      -30 - Math.random() * 20
    )
    
    const velocity = new THREE.Vector3(
      (endPosition.x - startPosition.x) * 0.02,
      0.3,
      (endPosition.z - startPosition.z) * 0.02
    )
    
    // 添加到漂流瓶列表
    const bottleData: BottleData = {
      mesh: this.currentBottle,
      message: message,
      velocity: velocity,
      life: 0,
      maxLife: 10 // 10秒后消失
    }
    
    this.bottles.push(bottleData)
    
    // 重置UI
    this.currentBottle = null
    this.emotionInput.value = ''
    this.emotionInput.disabled = false
    this.createBottleBtn.disabled = true
    this.throwBottleBtn.disabled = true
    this.isThrowingBottle = false
    
    // 显示投掷成功消息
    this.showThrowMessage()
  }

  private showThrowMessage(): void {
    const driftUI = document.getElementById('drift-bottle-ui')
    if (!driftUI) return
    
    const message = document.createElement('div')
    message.textContent = '漂流瓶已投入大海，愿它带走你的哀愁...'
    message.style.cssText = `
      position: absolute;
      bottom: -40px;
      left: 0;
      right: 0;
      text-align: center;
      color: #26d0ce;
      font-size: 12px;
      opacity: 0;
      transition: opacity 0.5s ease;
    `
    
    driftUI.appendChild(message)
    
    // 淡入动画
    setTimeout(() => {
      message.style.opacity = '1'
    }, 100)
    
    // 3秒后移除
    setTimeout(() => {
      message.style.opacity = '0'
      setTimeout(() => {
        if (message.parentNode) {
          message.parentNode.removeChild(message)
        }
      }, 500)
    }, 3000)
  }

  public update(deltaTime: number): void {
    // 更新所有漂流瓶
    for (let i = this.bottles.length - 1; i >= 0; i--) {
      const bottle = this.bottles[i]
      
      // 更新位置
      bottle.mesh.position.add(bottle.velocity.clone().multiplyScalar(deltaTime * 60))
      
      // 添加重力
      bottle.velocity.y -= 9.8 * deltaTime * 0.1
      
      // 如果碰到水面，添加浮力和波浪效果
      if (bottle.mesh.position.y <= -1.5) {
        bottle.mesh.position.y = -1.5 + Math.sin(bottle.life * 2) * 0.2
        bottle.velocity.y = Math.max(0, bottle.velocity.y)
        
        // 添加水平漂流
        bottle.velocity.x += (Math.random() - 0.5) * 0.01
        bottle.velocity.z -= 0.02 // 向远方漂流
      }
      
      // 添加旋转
      bottle.mesh.rotation.x += bottle.velocity.z * deltaTime
      bottle.mesh.rotation.z += bottle.velocity.x * deltaTime
      
      // 更新发光效果
      const glowMesh = bottle.mesh.children.find(child => 
        child instanceof THREE.Mesh && child.material instanceof THREE.ShaderMaterial
      ) as THREE.Mesh
      
      if (glowMesh && glowMesh.material instanceof THREE.ShaderMaterial) {
        glowMesh.material.uniforms.time.value = bottle.life
      }
      
      // 更新生命周期
      bottle.life += deltaTime
      
      // 距离太远或时间太长时移除
      const distance = bottle.mesh.position.distanceTo(new THREE.Vector3(0, 0, 0))
      if (bottle.life > bottle.maxLife || distance > 50) {
        this.scene.remove(bottle.mesh)
        this.bottles.splice(i, 1)
        
        // 清理资源
        bottle.mesh.children.forEach(child => {
          if (child instanceof THREE.Mesh) {
            child.geometry.dispose()
            if (child.material instanceof THREE.Material) {
              child.material.dispose()
            }
          }
        })
      }
    }
    
    // 更新当前瓶子的发光效果
    if (this.currentBottle) {
      const glowMesh = this.currentBottle.children.find(child => 
        child instanceof THREE.Mesh && child.material instanceof THREE.ShaderMaterial
      ) as THREE.Mesh
      
      if (glowMesh && glowMesh.material instanceof THREE.ShaderMaterial) {
        glowMesh.material.uniforms.time.value += deltaTime
      }
      
      // 轻微的浮动动画
      this.currentBottle.position.y = 1 + Math.sin(Date.now() * 0.002) * 0.1
      this.currentBottle.rotation.y += deltaTime * 0.5
    }
  }

  public dispose(): void {
    // 清理所有瓶子
    this.bottles.forEach(bottle => {
      this.scene.remove(bottle.mesh)
      bottle.mesh.children.forEach(child => {
        if (child instanceof THREE.Mesh) {
          child.geometry.dispose()
          if (child.material instanceof THREE.Material) {
            child.material.dispose()
          }
        }
      })
    })
    
    this.bottles = []
    
    if (this.currentBottle) {
      this.scene.remove(this.currentBottle)
      this.currentBottle = null
    }
  }
}
