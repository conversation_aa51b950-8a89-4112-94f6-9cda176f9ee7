import * as THREE from 'three'

export class OceanScene {
  private scene: THREE.Scene
  private oceanMesh: THREE.Mesh
  private skybox: THREE.Mesh
  private particles: THREE.Points
  private waves: THREE.Group

  constructor(scene: THREE.Scene) {
    this.scene = scene
    this.waves = new THREE.Group()
    
    this.createOcean()
    this.createSkybox()
    this.createParticles()
    this.createWaves()
  }

  private createOcean(): void {
    // 创建海洋平面
    const oceanGeometry = new THREE.PlaneGeometry(100, 100, 128, 128)
    
    // 海洋材质
    const oceanMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        color1: { value: new THREE.Color(0x0c1445) },
        color2: { value: new THREE.Color(0x1a2980) },
        color3: { value: new THREE.Color(0x26d0ce) }
      },
      vertexShader: `
        uniform float time;
        varying vec2 vUv;
        varying float vElevation;
        
        void main() {
          vUv = uv;
          
          vec4 modelPosition = modelMatrix * vec4(position, 1.0);
          
          // 创建波浪效果
          float elevation = sin(modelPosition.x * 0.3 + time * 0.5) * 0.3;
          elevation += sin(modelPosition.z * 0.2 + time * 0.3) * 0.2;
          elevation += sin(modelPosition.x * 0.1 + modelPosition.z * 0.1 + time * 0.1) * 0.5;
          
          modelPosition.y += elevation;
          vElevation = elevation;
          
          vec4 viewPosition = viewMatrix * modelPosition;
          vec4 projectedPosition = projectionMatrix * viewPosition;
          
          gl_Position = projectedPosition;
        }
      `,
      fragmentShader: `
        uniform vec3 color1;
        uniform vec3 color2;
        uniform vec3 color3;
        varying vec2 vUv;
        varying float vElevation;
        
        void main() {
          vec3 color = mix(color1, color2, vUv.y);
          color = mix(color, color3, vElevation * 0.5 + 0.5);
          
          // 添加一些透明度变化
          float alpha = 0.8 + vElevation * 0.2;
          
          gl_FragColor = vec4(color, alpha);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide
    })
    
    this.oceanMesh = new THREE.Mesh(oceanGeometry, oceanMaterial)
    this.oceanMesh.rotation.x = -Math.PI / 2
    this.oceanMesh.position.y = -2
    this.oceanMesh.receiveShadow = true
    this.scene.add(this.oceanMesh)
  }

  private createSkybox(): void {
    // 创建天空盒
    const skyGeometry = new THREE.SphereGeometry(50, 32, 32)
    const skyMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 }
      },
      vertexShader: `
        varying vec3 vWorldPosition;
        
        void main() {
          vec4 worldPosition = modelMatrix * vec4(position, 1.0);
          vWorldPosition = worldPosition.xyz;
          
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        varying vec3 vWorldPosition;
        
        void main() {
          vec3 direction = normalize(vWorldPosition);
          float y = direction.y;
          
          // 创建渐变天空
          vec3 topColor = vec3(0.05, 0.08, 0.27); // 深蓝
          vec3 horizonColor = vec3(0.1, 0.16, 0.5); // 中蓝
          vec3 bottomColor = vec3(0.15, 0.8, 0.8); // 青色
          
          vec3 color;
          if (y > 0.0) {
            color = mix(horizonColor, topColor, y);
          } else {
            color = mix(horizonColor, bottomColor, -y * 0.5);
          }
          
          // 添加一些星星效果
          float stars = step(0.98, sin(vWorldPosition.x * 100.0) * sin(vWorldPosition.y * 100.0) * sin(vWorldPosition.z * 100.0));
          color += stars * 0.3;
          
          gl_FragColor = vec4(color, 1.0);
        }
      `,
      side: THREE.BackSide
    })
    
    this.skybox = new THREE.Mesh(skyGeometry, skyMaterial)
    this.scene.add(this.skybox)
  }

  private createParticles(): void {
    // 创建漂浮的粒子效果
    const particleCount = 1000
    const positions = new Float32Array(particleCount * 3)
    const colors = new Float32Array(particleCount * 3)
    
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3
      
      // 随机位置
      positions[i3] = (Math.random() - 0.5) * 50
      positions[i3 + 1] = Math.random() * 20 - 5
      positions[i3 + 2] = (Math.random() - 0.5) * 50
      
      // 随机颜色 - 蓝色调
      const color = new THREE.Color()
      color.setHSL(0.6 + Math.random() * 0.1, 0.7, 0.5 + Math.random() * 0.3)
      colors[i3] = color.r
      colors[i3 + 1] = color.g
      colors[i3 + 2] = color.b
    }
    
    const particleGeometry = new THREE.BufferGeometry()
    particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))
    
    const particleMaterial = new THREE.PointsMaterial({
      size: 0.1,
      vertexColors: true,
      transparent: true,
      opacity: 0.6,
      blending: THREE.AdditiveBlending
    })
    
    this.particles = new THREE.Points(particleGeometry, particleMaterial)
    this.scene.add(this.particles)
  }

  private createWaves(): void {
    // 创建一些装饰性的波浪
    for (let i = 0; i < 5; i++) {
      const waveGeometry = new THREE.RingGeometry(2 + i * 2, 3 + i * 2, 32)
      const waveMaterial = new THREE.MeshBasicMaterial({
        color: 0x26d0ce,
        transparent: true,
        opacity: 0.1 - i * 0.02,
        side: THREE.DoubleSide
      })
      
      const wave = new THREE.Mesh(waveGeometry, waveMaterial)
      wave.rotation.x = -Math.PI / 2
      wave.position.y = -1.8 + i * 0.1
      
      this.waves.add(wave)
    }
    
    this.scene.add(this.waves)
  }

  public update(time: number): void {
    // 更新海洋动画
    if (this.oceanMesh.material instanceof THREE.ShaderMaterial) {
      this.oceanMesh.material.uniforms.time.value = time
    }
    
    // 更新天空盒
    if (this.skybox.material instanceof THREE.ShaderMaterial) {
      this.skybox.material.uniforms.time.value = time
    }
    
    // 旋转粒子
    this.particles.rotation.y += 0.001
    
    // 波浪动画
    this.waves.children.forEach((wave, index) => {
      wave.rotation.z += 0.005 * (index + 1)
      wave.scale.setScalar(1 + Math.sin(time + index) * 0.1)
    })
  }

  public dispose(): void {
    // 清理资源
    if (this.oceanMesh) {
      this.oceanMesh.geometry.dispose()
      if (this.oceanMesh.material instanceof THREE.Material) {
        this.oceanMesh.material.dispose()
      }
    }
    
    if (this.skybox) {
      this.skybox.geometry.dispose()
      if (this.skybox.material instanceof THREE.Material) {
        this.skybox.material.dispose()
      }
    }
    
    if (this.particles) {
      this.particles.geometry.dispose()
      if (this.particles.material instanceof THREE.Material) {
        this.particles.material.dispose()
      }
    }
    
    this.waves.children.forEach(wave => {
      if (wave instanceof THREE.Mesh) {
        wave.geometry.dispose()
        if (wave.material instanceof THREE.Material) {
          wave.material.dispose()
        }
      }
    })
  }
}
