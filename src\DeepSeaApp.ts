import * as THREE from 'three'
import { MemoryRecall } from './components/MemoryRecall'
import { DriftBottle } from './components/DriftBottle'
import { OceanScene } from './components/OceanScene'
import { VisualEffects } from './components/VisualEffects'
import { AudioManager } from './components/AudioManager'

export class DeepSeaApp {
  private scene: THREE.Scene
  private camera: THREE.PerspectiveCamera
  private renderer: THREE.WebGLRenderer
  private canvas: HTMLCanvasElement
  
  private oceanScene: OceanScene
  private memoryRecall: MemoryRecall
  private driftBottle: DriftBottle
  private visualEffects: VisualEffects
  private audioManager: AudioManager
  
  private animationId: number = 0
  private clock: THREE.Clock

  constructor() {
    this.clock = new THREE.Clock()
    this.scene = new THREE.Scene()
    this.canvas = document.getElementById('three-canvas') as HTMLCanvasElement
    
    // 设置相机
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    )
    this.camera.position.set(0, 5, 10)
    this.camera.lookAt(0, 0, 0)
    
    // 设置渲染器
    this.renderer = new THREE.WebGLRenderer({
      canvas: this.canvas,
      antialias: true,
      alpha: true
    })
    this.renderer.setSize(window.innerWidth, window.innerHeight)
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
    this.renderer.shadowMap.enabled = true
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
    
    // 初始化组件
    this.oceanScene = new OceanScene(this.scene)
    this.memoryRecall = new MemoryRecall(this.scene, this.camera, this.renderer)
    this.driftBottle = new DriftBottle(this.scene, this.camera)
    this.visualEffects = new VisualEffects(this.scene, this.camera, this.renderer)
    this.audioManager = new AudioManager()
  }

  public init(): void {
    this.setupLighting()
    this.setupEventListeners()
    this.setupAudio()
    this.animate()

    console.log('深海回响应用已启动')
  }

  private setupLighting(): void {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.3)
    this.scene.add(ambientLight)
    
    // 主光源 - 模拟月光
    const moonLight = new THREE.DirectionalLight(0x87ceeb, 0.8)
    moonLight.position.set(10, 20, 5)
    moonLight.castShadow = true
    moonLight.shadow.mapSize.width = 2048
    moonLight.shadow.mapSize.height = 2048
    moonLight.shadow.camera.near = 0.5
    moonLight.shadow.camera.far = 50
    moonLight.shadow.camera.left = -20
    moonLight.shadow.camera.right = 20
    moonLight.shadow.camera.top = 20
    moonLight.shadow.camera.bottom = -20
    this.scene.add(moonLight)
    
    // 深海蓝光
    const deepSeaLight = new THREE.PointLight(0x26d0ce, 0.5, 30)
    deepSeaLight.position.set(0, -5, 0)
    this.scene.add(deepSeaLight)
  }

  private setupAudio(): void {
    // 添加音频控制UI
    const audioToggle = document.createElement('button')
    audioToggle.textContent = '🔊'
    audioToggle.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
      background: rgba(0, 20, 40, 0.8);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      width: 50px;
      height: 50px;
      color: white;
      font-size: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
    `

    let audioEnabled = false
    audioToggle.addEventListener('click', () => {
      audioEnabled = !audioEnabled
      if (audioEnabled) {
        this.audioManager.enable()
        this.audioManager.playWaveAmbient()
        audioToggle.textContent = '🔊'
        audioToggle.style.background = 'rgba(38, 208, 206, 0.8)'
      } else {
        this.audioManager.disable()
        audioToggle.textContent = '🔇'
        audioToggle.style.background = 'rgba(0, 20, 40, 0.8)'
      }
    })

    document.body.appendChild(audioToggle)

    // 用户首次交互时启用音频
    const enableAudioOnFirstClick = () => {
      if (!audioEnabled) {
        audioEnabled = true
        this.audioManager.enable()
        this.audioManager.playWaveAmbient()
        audioToggle.textContent = '🔊'
        audioToggle.style.background = 'rgba(38, 208, 206, 0.8)'
        document.removeEventListener('click', enableAudioOnFirstClick)
      }
    }

    document.addEventListener('click', enableAudioOnFirstClick)
  }

  private setupEventListeners(): void {
    // 窗口大小调整
    window.addEventListener('resize', this.onWindowResize.bind(this))
    
    // 鼠标移动事件 - 用于记忆回溯功能
    this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this))
    this.canvas.addEventListener('click', this.onClick.bind(this))
    
    // 触摸事件支持
    this.canvas.addEventListener('touchmove', this.onTouchMove.bind(this))
    this.canvas.addEventListener('touchstart', this.onTouchStart.bind(this))
  }

  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight
    this.camera.updateProjectionMatrix()
    this.renderer.setSize(window.innerWidth, window.innerHeight)
  }

  private onMouseMove(event: MouseEvent): void {
    const mouse = new THREE.Vector2()
    mouse.x = (event.clientX / window.innerWidth) * 2 - 1
    mouse.y = -(event.clientY / window.innerHeight) * 2 + 1
    
    this.memoryRecall.onMouseMove(mouse)
  }

  private onClick(event: MouseEvent): void {
    const mouse = new THREE.Vector2()
    mouse.x = (event.clientX / window.innerWidth) * 2 - 1
    mouse.y = -(event.clientY / window.innerHeight) * 2 + 1
    
    this.memoryRecall.onClick(mouse)
  }

  private onTouchMove(event: TouchEvent): void {
    event.preventDefault()
    if (event.touches.length > 0) {
      const touch = event.touches[0]
      const mouse = new THREE.Vector2()
      mouse.x = (touch.clientX / window.innerWidth) * 2 - 1
      mouse.y = -(touch.clientY / window.innerHeight) * 2 + 1
      
      this.memoryRecall.onMouseMove(mouse)
    }
  }

  private onTouchStart(event: TouchEvent): void {
    event.preventDefault()
    if (event.touches.length > 0) {
      const touch = event.touches[0]
      const mouse = new THREE.Vector2()
      mouse.x = (touch.clientX / window.innerWidth) * 2 - 1
      mouse.y = -(touch.clientY / window.innerHeight) * 2 + 1
      
      this.memoryRecall.onClick(mouse)
    }
  }

  private animate(): void {
    this.animationId = requestAnimationFrame(this.animate.bind(this))
    
    const deltaTime = this.clock.getDelta()
    const elapsedTime = this.clock.getElapsedTime()
    
    // 更新各个组件
    this.oceanScene.update(elapsedTime)
    this.memoryRecall.update(deltaTime)
    this.driftBottle.update(deltaTime)
    this.visualEffects.update(elapsedTime)
    
    // 渲染场景
    this.renderer.render(this.scene, this.camera)
  }

  public dispose(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
    
    this.oceanScene.dispose()
    this.memoryRecall.dispose()
    this.driftBottle.dispose()
    this.visualEffects.dispose()
    this.audioManager.dispose()
    
    this.renderer.dispose()
  }
}
