import * as THREE from 'three'
import { AudioManager } from './AudioManager'

export class MemoryRecall {
  private scene: THREE.Scene
  private camera: THREE.PerspectiveCamera
  private renderer: THREE.WebGLRenderer
  private raycaster: THREE.Raycaster
  private audioManager: AudioManager | null = null
  
  private memoryFigure: THREE.Group
  private mistParticles: THREE.Points
  private clearRadius: number = 0
  private targetClearRadius: number = 0
  private maxClearRadius: number = 5
  private clearProgress: number = 0
  
  private mistPositions: Float32Array
  private mistOpacities: Float32Array
  private mistGeometry: THREE.BufferGeometry
  private mistMaterial: THREE.PointsMaterial

  constructor(scene: THREE.Scene, camera: THREE.PerspectiveCamera, renderer: THREE.WebGLRenderer, audioManager?: AudioManager) {
    this.scene = scene
    this.camera = camera
    this.renderer = renderer
    this.raycaster = new THREE.Raycaster()
    this.audioManager = audioManager || null

    this.createMemoryFigure()
    this.createMist()
    this.setupUI()
  }

  private createMemoryFigure(): void {
    this.memoryFigure = new THREE.Group()
    
    // 创建人形轮廓
    const figureGeometry = new THREE.CylinderGeometry(0.3, 0.5, 2, 8)
    const figureMaterial = new THREE.MeshLambertMaterial({
      color: 0x87ceeb,
      transparent: true,
      opacity: 0.1
    })
    
    const body = new THREE.Mesh(figureGeometry, figureMaterial)
    body.position.y = 1
    this.memoryFigure.add(body)
    
    // 头部
    const headGeometry = new THREE.SphereGeometry(0.3, 16, 16)
    const headMaterial = new THREE.MeshLambertMaterial({
      color: 0x87ceeb,
      transparent: true,
      opacity: 0.1
    })
    
    const head = new THREE.Mesh(headGeometry, headMaterial)
    head.position.y = 2.3
    this.memoryFigure.add(head)
    
    // 手臂
    const armGeometry = new THREE.CylinderGeometry(0.1, 0.1, 1.2, 8)
    const armMaterial = new THREE.MeshLambertMaterial({
      color: 0x87ceeb,
      transparent: true,
      opacity: 0.1
    })
    
    const leftArm = new THREE.Mesh(armGeometry, armMaterial)
    leftArm.position.set(-0.6, 1.5, 0)
    leftArm.rotation.z = Math.PI / 6
    this.memoryFigure.add(leftArm)
    
    const rightArm = new THREE.Mesh(armGeometry, armMaterial)
    rightArm.position.set(0.6, 1.5, 0)
    rightArm.rotation.z = -Math.PI / 6
    this.memoryFigure.add(rightArm)
    
    // 添加发光效果
    const glowGeometry = new THREE.SphereGeometry(1.5, 32, 32)
    const glowMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        opacity: { value: 0.1 }
      },
      vertexShader: `
        varying vec3 vNormal;
        varying vec3 vPosition;
        
        void main() {
          vNormal = normalize(normalMatrix * normal);
          vPosition = position;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform float opacity;
        varying vec3 vNormal;
        varying vec3 vPosition;
        
        void main() {
          float intensity = pow(0.7 - dot(vNormal, vec3(0.0, 0.0, 1.0)), 2.0);
          vec3 glow = vec3(0.5, 0.8, 1.0) * intensity;
          
          // 添加脉动效果
          float pulse = sin(time * 2.0) * 0.1 + 0.9;
          glow *= pulse;
          
          gl_FragColor = vec4(glow, intensity * opacity);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending,
      side: THREE.BackSide
    })
    
    const glow = new THREE.Mesh(glowGeometry, glowMaterial)
    glow.position.y = 1.5
    this.memoryFigure.add(glow)
    
    this.memoryFigure.position.set(0, 0, 0)
    this.scene.add(this.memoryFigure)
  }

  private createMist(): void {
    // 创建迷雾粒子系统
    const particleCount = 2000
    this.mistPositions = new Float32Array(particleCount * 3)
    this.mistOpacities = new Float32Array(particleCount)
    
    // 初始化粒子位置
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3
      
      // 在人形周围创建球形分布
      const radius = 2 + Math.random() * 3
      const theta = Math.random() * Math.PI * 2
      const phi = Math.random() * Math.PI
      
      this.mistPositions[i3] = radius * Math.sin(phi) * Math.cos(theta)
      this.mistPositions[i3 + 1] = radius * Math.cos(phi) + 1.5
      this.mistPositions[i3 + 2] = radius * Math.sin(phi) * Math.sin(theta)
      
      this.mistOpacities[i] = Math.random() * 0.8 + 0.2
    }
    
    this.mistGeometry = new THREE.BufferGeometry()
    this.mistGeometry.setAttribute('position', new THREE.BufferAttribute(this.mistPositions, 3))
    this.mistGeometry.setAttribute('opacity', new THREE.BufferAttribute(this.mistOpacities, 1))
    
    this.mistMaterial = new THREE.PointsMaterial({
      color: 0x87ceeb,
      size: 0.3,
      transparent: true,
      opacity: 0.6,
      blending: THREE.AdditiveBlending,
      vertexColors: false
    })
    
    this.mistParticles = new THREE.Points(this.mistGeometry, this.mistMaterial)
    this.scene.add(this.mistParticles)
  }

  private setupUI(): void {
    const memoryUI = document.getElementById('memory-recall-ui')
    if (memoryUI) {
      const progressText = document.createElement('div')
      progressText.id = 'memory-progress'
      progressText.style.marginTop = '10px'
      progressText.style.fontSize = '12px'
      progressText.style.opacity = '0.8'
      progressText.textContent = '清晰度: 0%'
      memoryUI.appendChild(progressText)
    }
  }

  public onMouseMove(mouse: THREE.Vector2): void {
    // 计算鼠标到人形的距离
    this.raycaster.setFromCamera(mouse, this.camera)
    
    const figurePosition = this.memoryFigure.position.clone()
    figurePosition.project(this.camera)
    
    const distance = mouse.distanceTo(new THREE.Vector2(figurePosition.x, figurePosition.y))
    
    // 如果鼠标靠近人形，增加清晰度
    if (distance < 0.3) {
      const oldRadius = this.targetClearRadius
      this.targetClearRadius = Math.min(this.maxClearRadius, this.targetClearRadius + 0.1)

      // 播放清晰化音效
      if (this.targetClearRadius > oldRadius && this.audioManager) {
        this.audioManager.playClarify()
      }
    }
  }

  public onClick(mouse: THREE.Vector2): void {
    // 点击时大幅增加清晰度
    this.raycaster.setFromCamera(mouse, this.camera)
    
    const figurePosition = this.memoryFigure.position.clone()
    figurePosition.project(this.camera)
    
    const distance = mouse.distanceTo(new THREE.Vector2(figurePosition.x, figurePosition.y))
    
    if (distance < 0.5) {
      const oldRadius = this.targetClearRadius
      this.targetClearRadius = Math.min(this.maxClearRadius, this.targetClearRadius + 0.5)

      // 播放强化清晰化音效
      if (this.targetClearRadius > oldRadius && this.audioManager) {
        this.audioManager.playClarify()
      }
    }
  }

  public update(deltaTime: number): void {
    // 平滑过渡清晰半径
    this.clearRadius = THREE.MathUtils.lerp(this.clearRadius, this.targetClearRadius, deltaTime * 2)
    this.clearProgress = this.clearRadius / this.maxClearRadius
    
    // 更新人形透明度
    this.memoryFigure.children.forEach(child => {
      if (child instanceof THREE.Mesh && child.material instanceof THREE.MeshLambertMaterial) {
        child.material.opacity = 0.1 + this.clearProgress * 0.8
      }
    })
    
    // 更新发光效果
    const glowMesh = this.memoryFigure.children.find(child => 
      child instanceof THREE.Mesh && child.material instanceof THREE.ShaderMaterial
    ) as THREE.Mesh
    
    if (glowMesh && glowMesh.material instanceof THREE.ShaderMaterial) {
      glowMesh.material.uniforms.time.value += deltaTime
      glowMesh.material.uniforms.opacity.value = 0.1 + this.clearProgress * 0.4
    }
    
    // 更新迷雾粒子
    this.updateMistParticles()
    
    // 更新UI
    this.updateUI()
  }

  private updateMistParticles(): void {
    const positions = this.mistGeometry.attributes.position.array as Float32Array
    const opacities = this.mistGeometry.attributes.opacity.array as Float32Array
    
    for (let i = 0; i < positions.length / 3; i++) {
      const i3 = i * 3
      const x = positions[i3]
      const y = positions[i3 + 1]
      const z = positions[i3 + 2]
      
      // 计算粒子到人形的距离
      const distance = Math.sqrt(x * x + (y - 1.5) * (y - 1.5) + z * z)
      
      // 如果在清晰半径内，降低透明度
      if (distance < this.clearRadius) {
        const fadeAmount = 1 - (distance / this.clearRadius)
        opacities[i] = Math.max(0, opacities[i] - fadeAmount * 0.02)
      } else {
        // 在清晰半径外，恢复透明度
        opacities[i] = Math.min(0.8, opacities[i] + 0.005)
      }
      
      // 添加轻微的漂浮动画
      positions[i3] += (Math.random() - 0.5) * 0.01
      positions[i3 + 1] += (Math.random() - 0.5) * 0.01
      positions[i3 + 2] += (Math.random() - 0.5) * 0.01
    }
    
    this.mistGeometry.attributes.position.needsUpdate = true
    this.mistGeometry.attributes.opacity.needsUpdate = true
  }

  private updateUI(): void {
    const progressElement = document.getElementById('memory-progress')
    if (progressElement) {
      const percentage = Math.round(this.clearProgress * 100)
      progressElement.textContent = `清晰度: ${percentage}%`
      
      if (percentage >= 100) {
        progressElement.textContent += ' - 记忆已完全清晰'
        progressElement.style.color = '#26d0ce'
      }
    }
  }

  public dispose(): void {
    if (this.mistGeometry) {
      this.mistGeometry.dispose()
    }
    
    if (this.mistMaterial) {
      this.mistMaterial.dispose()
    }
    
    this.memoryFigure.children.forEach(child => {
      if (child instanceof THREE.Mesh) {
        child.geometry.dispose()
        if (child.material instanceof THREE.Material) {
          child.material.dispose()
        }
      }
    })
  }
}
