* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
  background: linear-gradient(135deg, #0c1445 0%, #1a2980 50%, #26d0ce 100%);
  overflow: hidden;
  height: 100vh;
}

#app {
  position: relative;
  width: 100vw;
  height: 100vh;
}

#three-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

#ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.ui-panel {
  position: absolute;
  background: rgba(0, 20, 40, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  color: white;
  pointer-events: auto;
  transition: all 0.3s ease;
}

.ui-panel:hover {
  background: rgba(0, 20, 40, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
}

#memory-recall-ui {
  top: 30px;
  left: 30px;
  width: 300px;
}

#drift-bottle-ui {
  top: 30px;
  right: 30px;
  width: 320px;
}

.ui-panel h2 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #26d0ce;
  text-shadow: 0 0 10px rgba(38, 208, 206, 0.5);
}

.ui-panel p {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 15px;
  opacity: 0.9;
}

#emotion-input {
  width: 100%;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 10px;
  color: white;
  font-family: inherit;
  font-size: 14px;
  resize: none;
  margin-bottom: 15px;
}

#emotion-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

#emotion-input:focus {
  outline: none;
  border-color: #26d0ce;
  box-shadow: 0 0 10px rgba(38, 208, 206, 0.3);
}

button {
  background: linear-gradient(45deg, #1a2980, #26d0ce);
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  color: white;
  font-family: inherit;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 10px;
  margin-bottom: 10px;
}

button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(38, 208, 206, 0.4);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ui-panel {
    position: relative;
    margin: 10px;
    width: calc(100% - 20px);
  }

  #memory-recall-ui,
  #drift-bottle-ui {
    position: relative;
    top: auto;
    left: auto;
    right: auto;
  }

  #ui-overlay {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 20px;
  }
}
