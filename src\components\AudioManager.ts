export class AudioManager {
  private audioContext: AudioContext | null = null
  private masterGain: GainNode | null = null
  private isEnabled: boolean = false
  private sounds: Map<string, AudioBuffer> = new Map()

  constructor() {
    this.initAudio()
  }

  private async initAudio(): Promise<void> {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      this.masterGain = this.audioContext.createGain()
      this.masterGain.connect(this.audioContext.destination)
      this.masterGain.gain.value = 0.3 // 主音量
      
      // 创建程序化音效
      await this.createProgrammaticSounds()
      
      this.isEnabled = true
      console.log('音频系统已初始化')
    } catch (error) {
      console.warn('音频初始化失败:', error)
    }
  }

  private async createProgrammaticSounds(): Promise<void> {
    if (!this.audioContext) return

    // 创建海浪声音
    const waveBuffer = this.createWaveSound(5) // 5秒循环
    this.sounds.set('wave', waveBuffer)

    // 创建气泡声音
    const bubbleBuffer = this.createBubbleSound(0.5)
    this.sounds.set('bubble', bubbleBuffer)

    // 创建投掷声音
    const throwBuffer = this.createThrowSound(2)
    this.sounds.set('throw', throwBuffer)

    // 创建清晰化声音
    const clarifyBuffer = this.createClarifySound(1)
    this.sounds.set('clarify', clarifyBuffer)
  }

  private createWaveSound(duration: number): AudioBuffer {
    if (!this.audioContext) throw new Error('AudioContext not initialized')

    const sampleRate = this.audioContext.sampleRate
    const length = sampleRate * duration
    const buffer = this.audioContext.createBuffer(2, length, sampleRate)

    for (let channel = 0; channel < 2; channel++) {
      const channelData = buffer.getChannelData(channel)
      
      for (let i = 0; i < length; i++) {
        const time = i / sampleRate
        
        // 创建海浪的复合波形
        let sample = 0
        sample += Math.sin(2 * Math.PI * 80 * time) * 0.1 // 低频基础
        sample += Math.sin(2 * Math.PI * 120 * time) * 0.05 // 中频
        sample += (Math.random() - 0.5) * 0.3 // 白噪声模拟泡沫
        
        // 添加包络
        const envelope = Math.sin(Math.PI * time / duration)
        sample *= envelope * 0.2
        
        channelData[i] = sample
      }
    }

    return buffer
  }

  private createBubbleSound(duration: number): AudioBuffer {
    if (!this.audioContext) throw new Error('AudioContext not initialized')

    const sampleRate = this.audioContext.sampleRate
    const length = sampleRate * duration
    const buffer = this.audioContext.createBuffer(2, length, sampleRate)

    for (let channel = 0; channel < 2; channel++) {
      const channelData = buffer.getChannelData(channel)
      
      for (let i = 0; i < length; i++) {
        const time = i / sampleRate
        
        // 气泡的特征频率
        const freq = 800 + Math.sin(time * 20) * 200
        let sample = Math.sin(2 * Math.PI * freq * time)
        
        // 快速衰减包络
        const envelope = Math.exp(-time * 8)
        sample *= envelope * 0.1
        
        channelData[i] = sample
      }
    }

    return buffer
  }

  private createThrowSound(duration: number): AudioBuffer {
    if (!this.audioContext) throw new Error('AudioContext not initialized')

    const sampleRate = this.audioContext.sampleRate
    const length = sampleRate * duration
    const buffer = this.audioContext.createBuffer(2, length, sampleRate)

    for (let channel = 0; channel < 2; channel++) {
      const channelData = buffer.getChannelData(channel)
      
      for (let i = 0; i < length; i++) {
        const time = i / sampleRate
        
        // 投掷的风声效果
        let sample = (Math.random() - 0.5) * 2 // 白噪声
        
        // 低通滤波模拟风声
        const cutoff = 500 - time * 200 // 频率逐渐降低
        const filterStrength = Math.max(0, cutoff / 500)
        sample *= filterStrength
        
        // 包络
        const envelope = Math.exp(-time * 2) * Math.sin(Math.PI * time / duration)
        sample *= envelope * 0.15
        
        channelData[i] = sample
      }
    }

    return buffer
  }

  private createClarifySound(duration: number): AudioBuffer {
    if (!this.audioContext) throw new Error('AudioContext not initialized')

    const sampleRate = this.audioContext.sampleRate
    const length = sampleRate * duration
    const buffer = this.audioContext.createBuffer(2, length, sampleRate)

    for (let channel = 0; channel < 2; channel++) {
      const channelData = buffer.getChannelData(channel)
      
      for (let i = 0; i < length; i++) {
        const time = i / sampleRate
        
        // 清晰化的和谐音效
        let sample = 0
        sample += Math.sin(2 * Math.PI * 440 * time) * 0.3 // A4
        sample += Math.sin(2 * Math.PI * 554.37 * time) * 0.2 // C#5
        sample += Math.sin(2 * Math.PI * 659.25 * time) * 0.1 // E5
        
        // 渐强包络
        const envelope = Math.min(1, time * 3) * Math.exp(-time * 2)
        sample *= envelope * 0.1
        
        channelData[i] = sample
      }
    }

    return buffer
  }

  public playSound(soundName: string, volume: number = 1, loop: boolean = false): AudioBufferSourceNode | null {
    if (!this.isEnabled || !this.audioContext || !this.masterGain) return null

    const buffer = this.sounds.get(soundName)
    if (!buffer) {
      console.warn(`Sound '${soundName}' not found`)
      return null
    }

    // 确保音频上下文已恢复
    if (this.audioContext.state === 'suspended') {
      this.audioContext.resume()
    }

    const source = this.audioContext.createBufferSource()
    const gainNode = this.audioContext.createGain()

    source.buffer = buffer
    source.loop = loop
    gainNode.gain.value = volume

    source.connect(gainNode)
    gainNode.connect(this.masterGain)

    source.start()

    return source
  }

  public playWaveAmbient(): AudioBufferSourceNode | null {
    return this.playSound('wave', 0.3, true)
  }

  public playBubble(): AudioBufferSourceNode | null {
    return this.playSound('bubble', 0.5, false)
  }

  public playThrow(): AudioBufferSourceNode | null {
    return this.playSound('throw', 0.7, false)
  }

  public playClarify(): AudioBufferSourceNode | null {
    return this.playSound('clarify', 0.4, false)
  }

  public setMasterVolume(volume: number): void {
    if (this.masterGain) {
      this.masterGain.gain.value = Math.max(0, Math.min(1, volume))
    }
  }

  public enable(): void {
    this.isEnabled = true
    if (this.audioContext && this.audioContext.state === 'suspended') {
      this.audioContext.resume()
    }
  }

  public disable(): void {
    this.isEnabled = false
    if (this.audioContext && this.audioContext.state === 'running') {
      this.audioContext.suspend()
    }
  }

  public dispose(): void {
    if (this.audioContext) {
      this.audioContext.close()
      this.audioContext = null
    }
    this.masterGain = null
    this.sounds.clear()
    this.isEnabled = false
  }
}
