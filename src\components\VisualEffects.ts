import * as THREE from 'three'

export class VisualEffects {
  private scene: THREE.Scene
  private camera: THREE.PerspectiveCamera
  private renderer: THREE.WebGLRenderer
  
  private bubbles: THREE.Points
  private caustics: THREE.Mesh
  private volumetricLight: THREE.Mesh
  private underwaterParticles: THREE.Points

  constructor(scene: THREE.Scene, camera: THREE.PerspectiveCamera, renderer: THREE.WebGLRenderer) {
    this.scene = scene
    this.camera = camera
    this.renderer = renderer
    
    this.createBubbles()
    this.createCaustics()
    this.createVolumetricLight()
    this.createUnderwaterParticles()
  }

  private createBubbles(): void {
    // 创建气泡效果
    const bubbleCount = 200
    const positions = new Float32Array(bubbleCount * 3)
    const scales = new Float32Array(bubbleCount)
    const velocities = new Float32Array(bubbleCount * 3)
    
    for (let i = 0; i < bubbleCount; i++) {
      const i3 = i * 3
      
      // 随机位置
      positions[i3] = (Math.random() - 0.5) * 40
      positions[i3 + 1] = Math.random() * -10 - 5
      positions[i3 + 2] = (Math.random() - 0.5) * 40
      
      // 随机大小
      scales[i] = Math.random() * 0.3 + 0.1
      
      // 上升速度
      velocities[i3] = (Math.random() - 0.5) * 0.02
      velocities[i3 + 1] = Math.random() * 0.05 + 0.02
      velocities[i3 + 2] = (Math.random() - 0.5) * 0.02
    }
    
    const bubbleGeometry = new THREE.BufferGeometry()
    bubbleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
    bubbleGeometry.setAttribute('scale', new THREE.BufferAttribute(scales, 1))
    bubbleGeometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3))
    
    const bubbleMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        color: { value: new THREE.Color(0x87ceeb) }
      },
      vertexShader: `
        attribute float scale;
        attribute vec3 velocity;
        uniform float time;
        varying float vOpacity;
        
        void main() {
          vec3 pos = position;
          
          // 气泡上升动画
          pos.y += velocity.y * time * 10.0;
          pos.x += sin(time + position.y) * 0.5;
          
          // 如果气泡到达水面，重置位置
          if (pos.y > 5.0) {
            pos.y = -15.0;
          }
          
          vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
          gl_PointSize = scale * 100.0 / -mvPosition.z;
          gl_Position = projectionMatrix * mvPosition;
          
          // 根据深度计算透明度
          vOpacity = 1.0 - (pos.y + 15.0) / 20.0;
        }
      `,
      fragmentShader: `
        uniform vec3 color;
        varying float vOpacity;
        
        void main() {
          vec2 center = gl_PointCoord - vec2(0.5);
          float dist = length(center);
          
          if (dist > 0.5) discard;
          
          float alpha = 1.0 - dist * 2.0;
          alpha *= vOpacity * 0.6;
          
          gl_FragColor = vec4(color, alpha);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending
    })
    
    this.bubbles = new THREE.Points(bubbleGeometry, bubbleMaterial)
    this.scene.add(this.bubbles)
  }

  private createCaustics(): void {
    // 创建水面焦散效果
    const causticsGeometry = new THREE.PlaneGeometry(50, 50, 64, 64)
    const causticsMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        intensity: { value: 0.3 }
      },
      vertexShader: `
        varying vec2 vUv;
        varying vec3 vPosition;
        
        void main() {
          vUv = uv;
          vPosition = position;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform float intensity;
        varying vec2 vUv;
        varying vec3 vPosition;
        
        float random(vec2 st) {
          return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
        }
        
        float noise(vec2 st) {
          vec2 i = floor(st);
          vec2 f = fract(st);
          
          float a = random(i);
          float b = random(i + vec2(1.0, 0.0));
          float c = random(i + vec2(0.0, 1.0));
          float d = random(i + vec2(1.0, 1.0));
          
          vec2 u = f * f * (3.0 - 2.0 * f);
          
          return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
        }
        
        void main() {
          vec2 st = vUv * 10.0;
          
          // 创建动态的焦散图案
          float n1 = noise(st + time * 0.5);
          float n2 = noise(st * 2.0 - time * 0.3);
          float n3 = noise(st * 4.0 + time * 0.7);
          
          float caustic = n1 * 0.5 + n2 * 0.3 + n3 * 0.2;
          caustic = pow(caustic, 3.0) * intensity;
          
          vec3 color = vec3(0.2, 0.6, 1.0) * caustic;
          gl_FragColor = vec4(color, caustic);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending,
      side: THREE.DoubleSide
    })
    
    this.caustics = new THREE.Mesh(causticsGeometry, causticsMaterial)
    this.caustics.rotation.x = -Math.PI / 2
    this.caustics.position.y = -1.5
    this.scene.add(this.caustics)
  }

  private createVolumetricLight(): void {
    // 创建体积光效果
    const lightGeometry = new THREE.CylinderGeometry(0.1, 3, 10, 16, 1, true)
    const lightMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        color: { value: new THREE.Color(0x87ceeb) }
      },
      vertexShader: `
        varying vec2 vUv;
        varying vec3 vPosition;
        
        void main() {
          vUv = uv;
          vPosition = position;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec3 color;
        varying vec2 vUv;
        varying vec3 vPosition;
        
        void main() {
          float intensity = 1.0 - vUv.x;
          intensity *= sin(vUv.y * 3.14159);
          
          // 添加动态效果
          intensity *= 0.5 + 0.5 * sin(time * 2.0 + vPosition.y);
          
          vec3 finalColor = color * intensity;
          gl_FragColor = vec4(finalColor, intensity * 0.3);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending,
      side: THREE.DoubleSide
    })
    
    this.volumetricLight = new THREE.Mesh(lightGeometry, lightMaterial)
    this.volumetricLight.position.set(5, 0, -5)
    this.volumetricLight.rotation.z = Math.PI / 6
    this.scene.add(this.volumetricLight)
  }

  private createUnderwaterParticles(): void {
    // 创建水下漂浮粒子
    const particleCount = 500
    const positions = new Float32Array(particleCount * 3)
    const colors = new Float32Array(particleCount * 3)
    const sizes = new Float32Array(particleCount)
    
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3
      
      positions[i3] = (Math.random() - 0.5) * 60
      positions[i3 + 1] = Math.random() * 15 - 5
      positions[i3 + 2] = (Math.random() - 0.5) * 60
      
      const color = new THREE.Color()
      color.setHSL(0.55 + Math.random() * 0.1, 0.7, 0.3 + Math.random() * 0.4)
      colors[i3] = color.r
      colors[i3 + 1] = color.g
      colors[i3 + 2] = color.b
      
      sizes[i] = Math.random() * 0.05 + 0.02
    }
    
    const particleGeometry = new THREE.BufferGeometry()
    particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))
    particleGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1))
    
    const particleMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 }
      },
      vertexShader: `
        attribute float size;
        uniform float time;
        varying vec3 vColor;
        
        void main() {
          vColor = color;
          
          vec3 pos = position;
          pos.x += sin(time + position.y) * 0.5;
          pos.z += cos(time + position.x) * 0.3;
          
          vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
          gl_PointSize = size * 300.0 / -mvPosition.z;
          gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        uniform float time;
        varying vec3 vColor;
        
        void main() {
          vec2 center = gl_PointCoord - vec2(0.5);
          float dist = length(center);
          
          if (dist > 0.5) discard;
          
          float alpha = 1.0 - dist * 2.0;
          alpha *= 0.6;
          
          gl_FragColor = vec4(vColor, alpha);
        }
      `,
      transparent: true,
      vertexColors: true,
      blending: THREE.AdditiveBlending
    })
    
    this.underwaterParticles = new THREE.Points(particleGeometry, particleMaterial)
    this.scene.add(this.underwaterParticles)
  }

  public update(time: number): void {
    // 更新气泡
    if (this.bubbles.material instanceof THREE.ShaderMaterial) {
      this.bubbles.material.uniforms.time.value = time
    }
    
    // 更新焦散效果
    if (this.caustics.material instanceof THREE.ShaderMaterial) {
      this.caustics.material.uniforms.time.value = time
    }
    
    // 更新体积光
    if (this.volumetricLight.material instanceof THREE.ShaderMaterial) {
      this.volumetricLight.material.uniforms.time.value = time
    }
    this.volumetricLight.rotation.y += 0.005
    
    // 更新水下粒子
    if (this.underwaterParticles.material instanceof THREE.ShaderMaterial) {
      this.underwaterParticles.material.uniforms.time.value = time
    }
    this.underwaterParticles.rotation.y += 0.001
  }

  public dispose(): void {
    // 清理资源
    const meshes = [this.bubbles, this.caustics, this.volumetricLight, this.underwaterParticles]
    
    meshes.forEach(mesh => {
      if (mesh) {
        mesh.geometry.dispose()
        if (mesh.material instanceof THREE.Material) {
          mesh.material.dispose()
        }
        this.scene.remove(mesh)
      }
    })
  }
}
